/* 设计系统样式 */

/* 阴影系统 */
:root {
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.05), 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  --shadow-md: 0 4px 8px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.04);
  --shadow-lg: 0 12px 20px -3px rgba(0, 0, 0, 0.08), 0 4px 8px -2px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 20px 30px -5px rgba(0, 0, 0, 0.08), 0 10px 15px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.04);

  --shadow-blue-sm: 0 1px 3px 0 rgba(59, 130, 246, 0.08), 0 1px 2px 0 rgba(59, 130, 246, 0.04);
  --shadow-blue-md: 0 4px 8px -1px rgba(59, 130, 246, 0.15), 0 2px 4px -1px rgba(59, 130, 246, 0.08);
  --shadow-blue-lg: 0 12px 20px -3px rgba(59, 130, 246, 0.15), 0 4px 8px -2px rgba(59, 130, 246, 0.08);

  /* 教育主题彩色阴影系统 */
  --shadow-green-sm: 0 1px 3px 0 rgba(34, 197, 94, 0.08), 0 1px 2px 0 rgba(34, 197, 94, 0.04);
  --shadow-green-md: 0 4px 8px -1px rgba(34, 197, 94, 0.15), 0 2px 4px -1px rgba(34, 197, 94, 0.08);
  --shadow-green-lg: 0 12px 20px -3px rgba(34, 197, 94, 0.15), 0 4px 8px -2px rgba(34, 197, 94, 0.08);
  
  --shadow-purple-sm: 0 1px 3px 0 rgba(147, 51, 234, 0.08), 0 1px 2px 0 rgba(147, 51, 234, 0.04);
  --shadow-purple-md: 0 4px 8px -1px rgba(147, 51, 234, 0.15), 0 2px 4px -1px rgba(147, 51, 234, 0.08);
  --shadow-purple-lg: 0 12px 20px -3px rgba(147, 51, 234, 0.15), 0 4px 8px -2px rgba(147, 51, 234, 0.08);
  
  --shadow-orange-sm: 0 1px 3px 0 rgba(249, 115, 22, 0.08), 0 1px 2px 0 rgba(249, 115, 22, 0.04);
  --shadow-orange-md: 0 4px 8px -1px rgba(249, 115, 22, 0.15), 0 2px 4px -1px rgba(249, 115, 22, 0.08);
  --shadow-orange-lg: 0 12px 20px -3px rgba(249, 115, 22, 0.15), 0 4px 8px -2px rgba(249, 115, 22, 0.08);
  
  --shadow-teal-sm: 0 1px 3px 0 rgba(20, 184, 166, 0.08), 0 1px 2px 0 rgba(20, 184, 166, 0.04);
  --shadow-teal-md: 0 4px 8px -1px rgba(20, 184, 166, 0.15), 0 2px 4px -1px rgba(20, 184, 166, 0.08);
  --shadow-teal-lg: 0 12px 20px -3px rgba(20, 184, 166, 0.15), 0 4px 8px -2px rgba(20, 184, 166, 0.08);
  
  --shadow-indigo-sm: 0 1px 3px 0 rgba(99, 102, 241, 0.08), 0 1px 2px 0 rgba(99, 102, 241, 0.04);
  --shadow-indigo-md: 0 4px 8px -1px rgba(99, 102, 241, 0.15), 0 2px 4px -1px rgba(99, 102, 241, 0.08);
  --shadow-indigo-lg: 0 12px 20px -3px rgba(99, 102, 241, 0.15), 0 4px 8px -2px rgba(99, 102, 241, 0.08);

  --shadow-hover: 0 6px 16px rgba(0, 0, 0, 0.08), 0 3px 6px rgba(0, 0, 0, 0.04);
  --shadow-active: 0 2px 4px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
}

.dark {
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 8px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 12px 20px -3px rgba(0, 0, 0, 0.3), 0 4px 8px -2px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 30px -5px rgba(0, 0, 0, 0.3), 0 10px 15px -5px rgba(0, 0, 0, 0.2);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.2);

  --shadow-blue-sm: 0 1px 3px 0 rgba(59, 130, 246, 0.2), 0 1px 2px 0 rgba(59, 130, 246, 0.15);
  --shadow-blue-md: 0 4px 8px -1px rgba(59, 130, 246, 0.3), 0 2px 4px -1px rgba(59, 130, 246, 0.2);
  --shadow-blue-lg: 0 12px 20px -3px rgba(59, 130, 246, 0.3), 0 4px 8px -2px rgba(59, 130, 246, 0.2);

  /* 深色主题彩色阴影 */
  --shadow-green-sm: 0 1px 3px 0 rgba(34, 197, 94, 0.2), 0 1px 2px 0 rgba(34, 197, 94, 0.15);
  --shadow-green-md: 0 4px 8px -1px rgba(34, 197, 94, 0.3), 0 2px 4px -1px rgba(34, 197, 94, 0.2);
  --shadow-green-lg: 0 12px 20px -3px rgba(34, 197, 94, 0.3), 0 4px 8px -2px rgba(34, 197, 94, 0.2);
  
  --shadow-purple-sm: 0 1px 3px 0 rgba(147, 51, 234, 0.2), 0 1px 2px 0 rgba(147, 51, 234, 0.15);
  --shadow-purple-md: 0 4px 8px -1px rgba(147, 51, 234, 0.3), 0 2px 4px -1px rgba(147, 51, 234, 0.2);
  --shadow-purple-lg: 0 12px 20px -3px rgba(147, 51, 234, 0.3), 0 4px 8px -2px rgba(147, 51, 234, 0.2);
  
  --shadow-orange-sm: 0 1px 3px 0 rgba(249, 115, 22, 0.2), 0 1px 2px 0 rgba(249, 115, 22, 0.15);
  --shadow-orange-md: 0 4px 8px -1px rgba(249, 115, 22, 0.3), 0 2px 4px -1px rgba(249, 115, 22, 0.2);
  --shadow-orange-lg: 0 12px 20px -3px rgba(249, 115, 22, 0.3), 0 4px 8px -2px rgba(249, 115, 22, 0.2);
  
  --shadow-teal-sm: 0 1px 3px 0 rgba(20, 184, 166, 0.2), 0 1px 2px 0 rgba(20, 184, 166, 0.15);
  --shadow-teal-md: 0 4px 8px -1px rgba(20, 184, 166, 0.3), 0 2px 4px -1px rgba(20, 184, 166, 0.2);
  --shadow-teal-lg: 0 12px 20px -3px rgba(20, 184, 166, 0.3), 0 4px 8px -2px rgba(20, 184, 166, 0.2);
  
  --shadow-indigo-sm: 0 1px 3px 0 rgba(99, 102, 241, 0.2), 0 1px 2px 0 rgba(99, 102, 241, 0.15);
  --shadow-indigo-md: 0 4px 8px -1px rgba(99, 102, 241, 0.3), 0 2px 4px -1px rgba(99, 102, 241, 0.2);
  --shadow-indigo-lg: 0 12px 20px -3px rgba(99, 102, 241, 0.3), 0 4px 8px -2px rgba(99, 102, 241, 0.2);

  --shadow-hover: 0 6px 16px rgba(0, 0, 0, 0.25), 0 3px 6px rgba(0, 0, 0, 0.2);
  --shadow-active: 0 2px 4px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0, 0, 0, 0.15);
}

/* 圆角系统 */
:root {
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;
}

/* 间距系统 */
:root {
  --spacing-0: 0;
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-32: 8rem;
  --spacing-40: 10rem;
  --spacing-48: 12rem;
  --spacing-56: 14rem;
  --spacing-64: 16rem;
}

/* 字体系统 */
:root {
  --font-sans: system-ui, -apple-system, "Segoe UI", Roboto, Ubuntu, Cantarell, "Noto Sans", sans-serif, "Helvetica Neue", Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;

  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
}

/* 颜色系统 - 亮色主题 */
:root {
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  --color-primary-950: #172554;

  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;

  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;
  --color-success-950: #052e16;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;
  --color-warning-950: #451a03;

  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;
  --color-error-950: #450a0a;

  --color-background: #ffffff;
  --color-foreground: #111827;
  --color-muted: #6b7280;
  --color-border: #e5e7eb;
  --color-surface: #f9fafb;
  --color-surface-hover: #f3f4f6;
  --color-surface-active: #e5e7eb;
}

/* 颜色系统 - 暗色主题 */
.dark {
  --color-primary-50: #172554;
  --color-primary-100: #1e3a8a;
  --color-primary-200: #1e40af;
  --color-primary-300: #1d4ed8;
  --color-primary-400: #2563eb;
  --color-primary-500: #3b82f6;
  --color-primary-600: #60a5fa;
  --color-primary-700: #93c5fd;
  --color-primary-800: #bfdbfe;
  --color-primary-900: #dbeafe;
  --color-primary-950: #eff6ff;

  --color-background: #111827;
  --color-foreground: #f9fafb;
  --color-muted: #9ca3af;
  --color-border: #374151;
  --color-surface: #1f2937;
  --color-surface-hover: #374151;
  --color-surface-active: #4b5563;
}

/* 过渡系统 */
:root {
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;
  --transition-timing-default: cubic-bezier(0.4, 0, 0.2, 1);
  --transition-timing-in: cubic-bezier(0.4, 0, 1, 1);
  --transition-timing-out: cubic-bezier(0, 0, 0.2, 1);
  --transition-timing-linear: linear;
}

/* 布局系统 */
:root {
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  --z-index-0: 0;
  --z-index-10: 10;
  --z-index-20: 20;
  --z-index-30: 30;
  --z-index-40: 40;
  --z-index-50: 50;
  --z-index-auto: auto;
}

/* 组件样式 */
.ds-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal) var(--transition-timing-default);
  cursor: pointer;
}

.ds-button-primary {
  background-color: var(--color-primary-500);
  color: white;
}

.ds-button-primary:hover {
  background-color: var(--color-primary-600);
}

.ds-button-secondary {
  background-color: var(--color-gray-200);
  color: var(--color-gray-800);
}

.ds-button-secondary:hover {
  background-color: var(--color-gray-300);
}

.ds-button-outline {
  background-color: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-foreground);
}

.ds-button-outline:hover {
  background-color: var(--color-surface-hover);
}

.ds-card {
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.ds-input {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
  background-color: var(--color-background);
  color: var(--color-foreground);
  transition: border-color var(--transition-fast) var(--transition-timing-default);
}

.ds-input:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 2px var(--color-primary-100);
}

.dark .ds-input:focus {
  box-shadow: 0 0 0 2px var(--color-primary-900);
}

/* 工具类 */
.ds-shadow-sm { box-shadow: var(--shadow-sm); }
.ds-shadow-md { box-shadow: var(--shadow-md); }
.ds-shadow-lg { box-shadow: var(--shadow-lg); }
.ds-shadow-xl { box-shadow: var(--shadow-xl); }
.ds-shadow-2xl { box-shadow: var(--shadow-2xl); }
.ds-shadow-inner { box-shadow: var(--shadow-inner); }

.ds-rounded-sm { border-radius: var(--radius-sm); }
.ds-rounded-md { border-radius: var(--radius-md); }
.ds-rounded-lg { border-radius: var(--radius-lg); }
.ds-rounded-xl { border-radius: var(--radius-xl); }
.ds-rounded-2xl { border-radius: var(--radius-2xl); }
.ds-rounded-3xl { border-radius: var(--radius-3xl); }
.ds-rounded-full { border-radius: var(--radius-full); }

.ds-transition { transition: all var(--transition-normal) var(--transition-timing-default); }
.ds-transition-fast { transition: all var(--transition-fast) var(--transition-timing-default); }
.ds-transition-slow { transition: all var(--transition-slow) var(--transition-timing-default); }

/* 响应式工具 */
@media (max-width: 640px) {
  .ds-hidden-sm { display: none; }
}

@media (max-width: 768px) {
  .ds-hidden-md { display: none; }
}

@media (max-width: 1024px) {
  .ds-hidden-lg { display: none; }
}

@media (max-width: 1280px) {
  .ds-hidden-xl { display: none; }
}

/* 渐变系统 */
:root {
  /* 教育主题温暖渐变 */
  --gradient-warm-blue: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --gradient-warm-green: linear-gradient(135deg, #22c55e 0%, #15803d 100%);
  --gradient-warm-purple: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
  --gradient-warm-orange: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  --gradient-warm-teal: linear-gradient(135deg, #14b8a6 0%, #0f766e 100%);
  --gradient-warm-indigo: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  
  /* 教育氛围背景渐变 */
  --gradient-education-bg: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #f0f4ff 50%, #faf5ff 75%, #fef7ed 100%);
  --gradient-education-bg-dark: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #1e1b4b 50%, #312e81 75%, #451a03 100%);
  
  /* 微妙装饰渐变 */
  --gradient-subtle-blue: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.05));
  --gradient-subtle-purple: linear-gradient(45deg, rgba(147, 51, 234, 0.1), rgba(196, 181, 253, 0.05));
  --gradient-subtle-green: linear-gradient(45deg, rgba(34, 197, 94, 0.1), rgba(187, 247, 208, 0.05));
}

.dark {
  --gradient-education-bg: var(--gradient-education-bg-dark);
  --gradient-subtle-blue: linear-gradient(45deg, rgba(59, 130, 246, 0.15), rgba(147, 197, 253, 0.08));
  --gradient-subtle-purple: linear-gradient(45deg, rgba(147, 51, 234, 0.15), rgba(196, 181, 253, 0.08));
  --gradient-subtle-green: linear-gradient(45deg, rgba(34, 197, 94, 0.15), rgba(187, 247, 208, 0.08));
}

/* 微交互动画系统 */
@keyframes ds-float {
  0%, 100% { 
    transform: translateY(0px); 
  }
  50% { 
    transform: translateY(-2px); 
  }
}

@keyframes ds-bounce-subtle {
  0%, 100% { 
    transform: scale(1); 
  }
  50% { 
    transform: scale(1.05); 
  }
}

@keyframes ds-pulse-glow {
  0%, 100% { 
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4); 
  }
  50% { 
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0); 
  }
}

@keyframes ds-slide-in-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes ds-scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes ds-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 新增工具类 */
.ds-shadow-blue-sm { box-shadow: var(--shadow-blue-sm); }
.ds-shadow-blue-md { box-shadow: var(--shadow-blue-md); }
.ds-shadow-blue-lg { box-shadow: var(--shadow-blue-lg); }

.ds-shadow-green-sm { box-shadow: var(--shadow-green-sm); }
.ds-shadow-green-md { box-shadow: var(--shadow-green-md); }
.ds-shadow-green-lg { box-shadow: var(--shadow-green-lg); }

.ds-shadow-purple-sm { box-shadow: var(--shadow-purple-sm); }
.ds-shadow-purple-md { box-shadow: var(--shadow-purple-md); }
.ds-shadow-purple-lg { box-shadow: var(--shadow-purple-lg); }

.ds-shadow-orange-sm { box-shadow: var(--shadow-orange-sm); }
.ds-shadow-orange-md { box-shadow: var(--shadow-orange-md); }
.ds-shadow-orange-lg { box-shadow: var(--shadow-orange-lg); }

.ds-shadow-teal-sm { box-shadow: var(--shadow-teal-sm); }
.ds-shadow-teal-md { box-shadow: var(--shadow-teal-md); }
.ds-shadow-teal-lg { box-shadow: var(--shadow-teal-lg); }

.ds-shadow-indigo-sm { box-shadow: var(--shadow-indigo-sm); }
.ds-shadow-indigo-md { box-shadow: var(--shadow-indigo-md); }
.ds-shadow-indigo-lg { box-shadow: var(--shadow-indigo-lg); }

/* 微交互动画工具类 */
.ds-animate-float {
  animation: ds-float 3s ease-in-out infinite;
}

.ds-animate-bounce-subtle {
  animation: ds-bounce-subtle 2s ease-in-out infinite;
}

.ds-animate-pulse-glow {
  animation: ds-pulse-glow 2s infinite;
}

.ds-animate-slide-in-up {
  animation: ds-slide-in-up 0.3s ease-out;
}

.ds-animate-scale-in {
  animation: ds-scale-in 0.3s ease-out;
}

.ds-shimmer {
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.4), 
    transparent
  );
  background-size: 200% 100%;
  animation: ds-shimmer 2s infinite;
}

/* 增强的悬浮效果 */
.hover-float {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-float:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
}

.hover-float:active {
  transform: translateY(0);
  box-shadow: var(--shadow-active);
}

/* 教育主题特殊效果 */
.education-glow {
  position: relative;
  overflow: hidden;
}

.education-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(59, 130, 246, 0.1), 
    transparent
  );
  transition: left 0.6s ease;
}

.education-glow:hover::before {
  left: 100%;
}

/* 焦点环增强 */
.ds-focus-ring {
  transition: all 0.3s ease;
}

.ds-focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.ds-focus-ring:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* 渐变工具类 */
.ds-gradient-warm-blue { background: var(--gradient-warm-blue); }
.ds-gradient-warm-green { background: var(--gradient-warm-green); }
.ds-gradient-warm-purple { background: var(--gradient-warm-purple); }
.ds-gradient-warm-orange { background: var(--gradient-warm-orange); }
.ds-gradient-warm-teal { background: var(--gradient-warm-teal); }
.ds-gradient-warm-indigo { background: var(--gradient-warm-indigo); }

.ds-gradient-education-bg { background: var(--gradient-education-bg); }
.ds-gradient-subtle-blue { background: var(--gradient-subtle-blue); }
.ds-gradient-subtle-purple { background: var(--gradient-subtle-purple); }
.ds-gradient-subtle-green { background: var(--gradient-subtle-green); }
